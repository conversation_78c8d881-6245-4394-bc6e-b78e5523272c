@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
}

html {
  scroll-behavior: smooth;
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.6s ease-out forwards;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Smooth focus styles */
button:focus,
input:focus,
textarea:focus,
select:focus {
  outline: 2px solid #00FFD1;
  outline-offset: 2px;
}

/* Enhanced button hover effects */
button {
  transition: all 0.2s ease-in-out;
}

/* Loading animation for AI mockup */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Enhanced animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(0, 255, 209, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(0, 255, 209, 0.6);
  }
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

/* Improved hover effects */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Staggered animations */
.stagger-animation {
  opacity: 0;
  transform: translateY(30px);
  animation: fade-in-up 0.6s ease-out forwards;
}

.stagger-animation:nth-child(1) { animation-delay: 0.1s; }
.stagger-animation:nth-child(2) { animation-delay: 0.2s; }
.stagger-animation:nth-child(3) { animation-delay: 0.3s; }
.stagger-animation:nth-child(4) { animation-delay: 0.4s; }
.stagger-animation:nth-child(5) { animation-delay: 0.5s; }

/* Responsive spacing utilities */
.section-padding {
  @apply py-12 sm:py-16 md:py-20 lg:py-24 xl:py-28;
}

.container-padding {
  @apply px-4 sm:px-6 lg:px-8 xl:px-12;
}

.header-offset {
  @apply pt-14 sm:pt-16 lg:pt-20;
}

/* Responsive text utilities */
.heading-xl {
  @apply text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl;
}

.heading-lg {
  @apply text-2xl sm:text-3xl md:text-4xl lg:text-5xl;
}

.heading-md {
  @apply text-xl sm:text-2xl md:text-3xl lg:text-4xl;
}

.heading-sm {
  @apply text-lg sm:text-xl md:text-2xl lg:text-3xl;
}

.text-responsive {
  @apply text-sm sm:text-base md:text-lg;
}

.text-responsive-sm {
  @apply text-xs sm:text-sm md:text-base;
}